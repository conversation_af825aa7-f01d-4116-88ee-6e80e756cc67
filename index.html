<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>名师简介 | 赶考状元</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        html {
            -webkit-user-select: none;
            user-select: none;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        :root {
            --primary: #2c5aa0;
            --secondary: #4a90e2;
            --accent: #ff6b35;
            --light: #f8f9fa;
            --gray: #666;
            --card-bg: rgba(255, 255, 255, 0.95);
            --shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        body {
            background: var(--gradient-bg);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            padding: 0;
        }
        
        .container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }
        
        /* 头部区域 */
        .header-section {
            background: var(--gradient-bg);
            padding: 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 20s infinite linear;
        }
        
        @keyframes float {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        .logo {
            color: white;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }
        
        .logo span {
            color: #ffd700;
        }
        
        .subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            letter-spacing: 2px;
            position: relative;
            z-index: 2;
        }
        
        /* 教师卡片 */
        .teacher-card {
            background: white;
            margin: -30px 20px 20px;
            border-radius: 20px;
            box-shadow: var(--shadow);
            overflow: hidden;
            position: relative;
            z-index: 3;
        }
        
        .teacher-header {
            padding: 30px 20px 20px;
            text-align: center;
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
            position: relative;
        }
        
        .teacher-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid white;
            object-fit: cover;
            margin: 0 auto 20px;
            display: block;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        
        .teacher-name {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
            color: var(--primary);
        }
        
        .teacher-subject {
            font-size: 16px;
            color: var(--gray);
            margin-bottom: 20px;
        }
        
        .teacher-stats {
            display: flex;
            justify-content: space-around;
            padding: 0 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: 700;
            color: var(--secondary);
            display: block;
        }
        
        .stat-label {
            font-size: 12px;
            color: var(--gray);
            margin-top: 4px;
        }
        
        /* 内容区域 */
        .content-section {
            padding: 0 20px 20px;
        }
        
        .section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            border: 1px solid #f0f0f0;
        }
        
        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            color: var(--primary);
            font-size: 18px;
            font-weight: 600;
        }
        
        .section-title i {
            margin-right: 10px;
            color: var(--accent);
            font-size: 20px;
            width: 24px;
            text-align: center;
        }
        
        .section-content {
            color: #444;
            line-height: 1.7;
        }
        
        /* 教师简介样式 */
        .intro-item {
            margin-bottom: 16px;
            font-size: 15px;
            line-height: 1.6;
            display: flex;
            align-items: flex-start;
        }
        
        .intro-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: var(--accent);
            color: white;
            border-radius: 50%;
            font-weight: 700;
            font-size: 12px;
            margin-right: 12px;
            flex-shrink: 0;
            margin-top: 2px;
        }
        
        /* 教学风格卡片 */
        .teaching-styles {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-top: 16px;
        }
        
        .style-card {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            border: 1px solid #e8f2ff;
        }
        
        .style-card i {
            font-size: 24px;
            color: var(--secondary);
            margin-bottom: 8px;
        }
        
        .style-title {
            font-weight: 600;
            margin-bottom: 6px;
            color: var(--primary);
            font-size: 14px;
        }
        
        .style-card p {
            font-size: 12px;
            color: var(--gray);
            line-height: 1.4;
        }
        
        /* 评价区域 */
        .reviews-container {
            display: grid;
            gap: 16px;
            margin-top: 16px;
        }
        
        .review-item {
            background: #f8f9ff;
            border-radius: 12px;
            padding: 16px;
            border: 1px solid #e8f2ff;
        }
        
        .review-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .review-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            margin-right: 10px;
            object-fit: cover;
        }
        
        .review-info {
            flex: 1;
        }
        
        .review-name {
            font-weight: 600;
            font-size: 14px;
            color: var(--primary);
        }
        
        .review-date {
            font-size: 12px;
            color: var(--gray);
        }
        
        .review-stars {
            color: #ffd700;
            margin: 8px 0;
            font-size: 14px;
        }
        
        .review-content {
            font-size: 14px;
            color: #555;
            font-style: italic;
            line-height: 1.5;
        }
        
        /* 截图网格 */
        .screenshot-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 16px;
        }
        
        .screenshot {
            aspect-ratio: 3/4;
            background: #f0f0f0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray);
            font-size: 12px;
            border: 1px solid #e0e0e0;
        }
        
        /* 页脚 */
        .footer {
            text-align: center;
            padding: 20px;
            color: var(--gray);
            font-size: 12px;
            background: #f8f9ff;
        }
        
        /* 响应式调整 */
        @media (max-width: 375px) {
            .container {
                max-width: 100%;
            }
            
            .teacher-card {
                margin: -30px 15px 15px;
            }
            
            .content-section {
                padding: 0 15px 15px;
            }
            
            .section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <div class="header-section">
            <div class="logo">赶考<span>状元</span></div>
            <div class="subtitle">LEADING EDUCATION GROUP</div>
        </div>
        
        <!-- 教师卡片 -->
        <div class="teacher-card">
            <div class="teacher-header">
                <img src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80" alt="张明华老师" class="teacher-avatar">
                <h1 class="teacher-name">张明华 老师</h1>
                <div class="teacher-subject">高中数学 | 教研组长</div>
                <div class="teacher-stats">
                    <div class="stat-item">
                        <span class="stat-value">12年</span>
                        <div class="stat-label">教龄</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">98%</span>
                        <div class="stat-label">提分率</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">1500+</span>
                        <div class="stat-label">学生</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-section">
            <!-- 教师简介 -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-user-graduate"></i>
                    <h2>教师简介</h2>
                </div>
                <div class="section-content">
                    <div class="intro-item">
                        <span class="intro-number">1</span>
                        <span>6年以上中学数学教学经验，创新备考生辅导教育机构经验，曾任数学学科组组长，累计授课学生达3000+</span>
                    </div>
                    <div class="intro-item">
                        <span class="intro-number">2</span>
                        <span>善于根据学生基础制定针对性教学方案，助力学生成绩提升720-40分</span>
                    </div>
                    <div class="intro-item">
                        <span class="intro-number">3</span>
                        <span>精通各种学习方法，高中数学重难点，深谙考试重点难点</span>
                    </div>
                </div>
            </div>

            <!-- 伴学经历 -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-clock"></i>
                    <h2>伴学经历</h2>
                </div>
                <div class="section-content">
                    <p>张老师每周可提供<strong>20小时</strong>的个性化辅导时间，包括：</p>
                    <ul style="padding-left: 20px; margin: 15px 0;">
                        <li>每周一、三、五晚 19:00-21:00（小组课）</li>
                        <li>每周二、四晚 19:00-21:00（1对1辅导）</li>
                        <li>周六全天 9:00-17:00（专题强化班）</li>
                    </ul>
                    <p>张老师坚持"精耕细作"的教学理念，每学期仅接收<strong>30名</strong>学生，确保每位学生都能获得充分的关注和个性化指导。他承诺24小时内解答学生疑问，每周提供学习反馈报告。</p>
                </div>
            </div>

            <!-- 伴学风格 -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-chalkboard-teacher"></i>
                    <h2>伴学风格</h2>
                </div>
                <div class="teaching-styles">
                    <div class="style-card">
                        <i class="fas fa-lightbulb"></i>
                        <div class="style-title">启发式教学</div>
                        <p>通过问题引导思考，激发学生自主探究能力</p>
                    </div>
                    <div class="style-card">
                        <i class="fas fa-puzzle-piece"></i>
                        <div class="style-title">体系化构建</div>
                        <p>帮助建立完整的知识框架，融会贯通</p>
                    </div>
                    <div class="style-card">
                        <i class="fas fa-heart"></i>
                        <div class="style-title">激励型互动</div>
                        <p>善于发现学生闪光点，增强学习信心</p>
                    </div>
                    <div class="style-card">
                        <i class="fas fa-laugh-beam"></i>
                        <div class="style-title">轻松氛围</div>
                        <p>幽默风趣的教学语言，化解学习压力</p>
                    </div>
                </div>
            </div>

            <!-- 学员评价 -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-star"></i>
                    <h2>学员评价</h2>
                </div>

                <div class="reviews-container">
                    <div class="review-item">
                        <div class="review-header">
                            <img src="https://randomuser.me/api/portraits/women/45.jpg" class="review-avatar">
                            <div class="review-info">
                                <div class="review-name">李同学</div>
                                <div class="review-date">2023-06-15</div>
                            </div>
                        </div>
                        <div class="review-stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="review-content">
                            "张老师的函数专题课让我突破了瓶颈，原来觉得很难的知识点，经过他的讲解变得特别清晰易懂。"
                        </div>
                    </div>

                    <div class="review-item">
                        <div class="review-header">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" class="review-avatar">
                            <div class="review-info">
                                <div class="review-name">王同学</div>
                                <div class="review-date">2023-05-22</div>
                            </div>
                        </div>
                        <div class="review-stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="review-content">
                            "三个月时间数学从90分提升到135分！张老师的解题思路太神奇了，每次都能直击要害。"
                        </div>
                    </div>
                </div>

                <h3 style="margin: 20px 0 12px; color: var(--primary); font-size: 16px;">学员反馈截图</h3>
                <div class="screenshot-grid">
                    <div class="screenshot">反馈截图1</div>
                    <div class="screenshot">反馈截图2</div>
                    <div class="screenshot">反馈截图3</div>
                    <div class="screenshot">反馈截图4</div>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <p>&copy; 2023 赶考状元教育集团 | 专业教育，成就未来</p>
        </div>
    </div>
</body>
</html>
