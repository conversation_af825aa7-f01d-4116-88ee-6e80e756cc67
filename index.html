<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>名师简介 | 赶考状元</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }
        
        :root {
            --primary: #0a192f;
            --secondary: #64ffda;
            --accent: #ffd700;
            --light: #f8f9fa;
            --gray: #8892b0;
            --card-bg: rgba(255, 255, 255, 0.95);
            --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
            color: #333;
            line-height: 1.6;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
        }
        
        .logo {
            font-size: 28px;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 10px;
        }
        
        .logo span {
            color: var(--accent);
        }
        
        .subtitle {
            color: var(--gray);
            font-size: 16px;
            letter-spacing: 2px;
        }
        
        /* 教师资料卡片 */
        .teacher-card {
            background: var(--card-bg);
            border-radius: 20px;
            box-shadow: var(--shadow);
            overflow: hidden;
            margin-bottom: 30px;
            transition: transform 0.3s ease;
        }
        
        .teacher-card:hover {
            transform: translateY(-5px);
        }
        
        .teacher-header {
            display: flex;
            align-items: center;
            padding: 30px;
            background: linear-gradient(to right, var(--primary), #1a365d);
            color: white;
        }
        
        .teacher-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid var(--accent);
            object-fit: cover;
            margin-right: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .teacher-info {
            flex: 1;
        }
        
        .teacher-name {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 5px;
            color: var(--accent);
        }
        
        .teacher-subject {
            font-size: 18px;
            margin-bottom: 15px;
            color: #fff;
            opacity: 0.9;
        }
        
        .teacher-stats {
            display: flex;
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 22px;
            font-weight: 700;
            color: var(--secondary);
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.8;
        }
        
        /* 内容部分 */
        .section {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow);
        }
        
        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--secondary);
            color: var(--primary);
        }
        
        .section-title i {
            margin-right: 12px;
            color: var(--accent);
            font-size: 24px;
        }
        
        .section-content {
            line-height: 1.8;
            color: #444;
        }
        
        /* 教学风格 */
        .teaching-styles {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .style-card {
            background: #f0f8ff;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .style-card:hover {
            background: #e1f0fa;
            transform: translateY(-3px);
        }
        
        .style-card i {
            font-size: 32px;
            color: var(--primary);
            margin-bottom: 15px;
        }
        
        .style-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--primary);
        }
        
        /* 评价部分 */
        .reviews-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .review-item {
            background: #fff;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid #eee;
        }
        
        .review-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .review-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
            object-fit: cover;
        }
        
        .review-info {
            flex: 1;
        }
        
        .review-name {
            font-weight: 600;
        }
        
        .review-date {
            font-size: 12px;
            color: var(--gray);
        }
        
        .review-stars {
            color: var(--accent);
            margin: 10px 0;
        }
        
        .review-content {
            font-size: 14px;
            color: #555;
            font-style: italic;
        }
        
        .screenshot-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .screenshot {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            aspect-ratio: 3/4;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray);
            font-size: 14px;
        }
        
        .screenshot img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        /* 页脚 */
        footer {
            text-align: center;
            padding: 30px;
            color: var(--gray);
            font-size: 14px;
            margin-top: 20px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .teacher-header {
                flex-direction: column;
                text-align: center;
            }
            
            .teacher-avatar {
                margin-right: 0;
                margin-bottom: 20px;
            }
            
            .teacher-stats {
                justify-content: center;
                margin-top: 20px;
            }
            
            .section {
                padding: 20px;
            }
        }
        
        @media (max-width: 480px) {
            .teaching-styles {
                grid-template-columns: 1fr;
            }
            
            .teacher-name {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">赶考<span>状元</span></div>
            <div class="subtitle">LEADING EDUCATION GROUP</div>
        </header>
        
        <!-- 教师资料卡片 -->
        <div class="teacher-card">
            <div class="teacher-header">
                <img src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80" alt="张明华老师" class="teacher-avatar">
                <div class="teacher-info">
                    <h1 class="teacher-name">张明华 老师</h1>
                    <div class="teacher-subject">高中数学 | 教研组长</div>
                    <div class="teacher-stats">
                        <div class="stat-item">
                            <div class="stat-value">12年</div>
                            <div class="stat-label">教龄</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">98%</div>
                            <div class="stat-label">提分率</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">1500+</div>
                            <div class="stat-label">学生</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 教师简介 -->
        <div class="section">
            <div class="section-title">
                <i class="fas fa-user-graduate"></i>
                <h2>教师简介</h2>
            </div>
            <div class="section-content">
                <div class="intro-item">6年以上中学数学教学经验，创新备考生辅导教育机构经验，曾任数学学科组组长，累计授课学生达3000+</div>
                <div class="intro-item">善于根据学生基础制定针对性教学方案，助力学生成绩提升720-40分</div>
                <div class="intro-item">精通各种学习方法，高中数学重难点，深谙考试重点难点</div>
            </div>
        </div>
        
        <!-- 伴学经历 -->
        <div class="section">
            <div class="section-title">
                <i class="fas fa-battery-full"></i>
                <h2>伴学精力</h2>
            </div>
            <div class="section-content">
                <p>张老师每周可提供<strong>20小时</strong>的个性化辅导时间，包括：</p>
                <ul style="padding-left: 20px; margin: 15px 0;">
                    <li>每周一、三、五晚 19:00-21:00（小组课）</li>
                    <li>每周二、四晚 19:00-21:00（1对1辅导）</li>
                    <li>周六全天 9:00-17:00（专题强化班）</li>
                </ul>
                <p>张老师坚持"精耕细作"的教学理念，每学期仅接收<strong>30名</strong>学生，确保每位学生都能获得充分的关注和个性化指导。他承诺24小时内解答学生疑问，每周提供学习反馈报告。</p>
            </div>
        </div>
        
        <!-- 伴学风格 -->
        <div class="section">
            <div class="section-title">
                <i class="fas fa-chalkboard-teacher"></i>
                <h2>伴学风格</h2>
            </div>
            <div class="teaching-styles">
                <div class="style-card">
                    <i class="fas fa-lightbulb"></i>
                    <div class="style-title">启发式教学</div>
                    <p>通过问题引导思考，激发学生自主探究能力</p>
                </div>
                <div class="style-card">
                    <i class="fas fa-puzzle-piece"></i>
                    <div class="style-title">体系化构建</div>
                    <p>帮助建立完整的知识框架，融会贯通</p>
                </div>
                <div class="style-card">
                    <i class="fas fa-heart"></i>
                    <div class="style-title">激励型互动</div>
                    <p>善于发现学生闪光点，增强学习信心</p>
                </div>
                <div class="style-card">
                    <i class="fas fa-laugh-beam"></i>
                    <div class="style-title">轻松氛围</div>
                    <p>幽默风趣的教学语言，化解学习压力</p>
                </div>
            </div>
        </div>
        
        <!-- 好评反馈 -->
        <div class="section">
            <div class="section-title">
                <i class="fas fa-star"></i>
                <h2>学员评价</h2>
            </div>
            
            <div class="reviews-container">
                <div class="review-item">
                    <div class="review-header">
                        <img src="https://randomuser.me/api/portraits/women/45.jpg" class="review-avatar">
                        <div class="review-info">
                            <div class="review-name">李同学</div>
                            <div class="review-date">2023-06-15</div>
                        </div>
                    </div>
                    <div class="review-stars">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="review-content">
                        "张老师的函数专题课让我突破了瓶颈，原来觉得很难的知识点，经过他的讲解变得特别清晰易懂。"
                    </div>
                </div>
                
                <div class="review-item">
                    <div class="review-header">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" class="review-avatar">
                        <div class="review-info">
                            <div class="review-name">王同学</div>
                            <div class="review-date">2023-05-22</div>
                        </div>
                    </div>
                    <div class="review-stars">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="review-content">
                        "三个月时间数学从90分提升到135分！张老师的解题思路太神奇了，每次都能直击要害。"
                    </div>
                </div>
            </div>
            
            <h3 style="margin: 25px 0 15px; color: var(--primary);">学员反馈截图</h3>
            <div class="screenshot-grid">
                <div class="screenshot">反馈截图1</div>
                <div class="screenshot">反馈截图2</div>
                <div class="screenshot">反馈截图3</div>
                <div class="screenshot">反馈截图4</div>
            </div>
        </div>
        
