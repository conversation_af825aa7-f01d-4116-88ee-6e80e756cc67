<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>名师简介 | 菁英教育</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Nunito:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary: #4361ee;
            --secondary: #3a0ca3;
            --accent: #f72585;
            --light: #f8f9ff;
            --text: #2b2d42;
            --text-light: #8d99ae;
            --card-bg: #ffffff;
            --gradient: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
            --shadow: 0 12px 30px rgba(67, 97, 238, 0.15);
            --border-radius: 18px;
        }
        
        body {
            background: linear-gradient(135deg, #f0f4ff 0%, #fafcff 100%);
            color: var(--text);
            font-family: 'Nunito', sans-serif;
            line-height: 1.7;
            padding: 20px;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        body::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 380px;
            background: var(--gradient);
            z-index: -1;
            border-bottom-left-radius: 50% 20%;
            border-bottom-right-radius: 50% 20%;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            position: relative;
        }
        
        /* 头部样式 */
        header {
            text-align: center;
            margin: 40px 0 60px;
            padding: 20px;
            position: relative;
            z-index: 10;
            animation: fadeIn 1s ease;
        }
        
        .logo {
            font-family: 'Noto Serif SC', serif;
            font-size: 42px;
            font-weight: 700;
            color: white;
            margin-bottom: 10px;
            letter-spacing: 1px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
        }
        
        .logo span {
            color: #ffd166;
        }
        
        .subtitle {
            color: rgba(255, 255, 255, 0.85);
            font-size: 18px;
            letter-spacing: 4px;
            font-weight: 300;
            margin-top: -5px;
        }
        
        /* 教师资料卡片 */
        .teacher-card {
            background: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
            margin-bottom: 50px;
            position: relative;
            z-index: 2;
            transform: translateY(0);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            animation: slideUp 0.8s ease;
        }
        
        .teacher-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(67, 97, 238, 0.25);
        }
        
        .teacher-header {
            display: flex;
            align-items: center;
            padding: 40px;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%234361ee" opacity="0.05"/><path d="M0 50 Q 50 30, 100 50 Q 50 70, 0 50" fill="%234361ee" opacity="0.1"/></svg>');
            background-size: cover;
            position: relative;
            overflow: hidden;
        }
        
        .teacher-header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
            z-index: 1;
        }
        
        .teacher-avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            border: 5px solid white;
            object-fit: cover;
            margin-right: 30px;
            box-shadow: 0 15px 30px rgba(67, 97, 238, 0.3);
            position: relative;
            z-index: 2;
            transition: all 0.4s ease;
        }
        
        .teacher-info {
            flex: 1;
            position: relative;
            z-index: 2;
        }
        
        .teacher-name {
            font-family: 'Noto Serif SC', serif;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 8px;
            color: var(--secondary);
            background: linear-gradient(to right, var(--secondary), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: 0.5px;
        }
        
        .teacher-subject {
            font-size: 22px;
            margin-bottom: 20px;
            color: var(--primary);
            font-weight: 600;
        }
        
        .teacher-tags {
            display: flex;
            gap: 12px;
            margin-bottom: 25px;
            flex-wrap: wrap;
        }
        
        .teacher-tag {
            display: inline-block;
            background: rgba(67, 97, 238, 0.1);
            color: var(--primary);
            padding: 8px 18px;
            border-radius: 30px;
            font-size: 15px;
            font-weight: 600;
            border: 1px solid rgba(67, 97, 238, 0.2);
            transition: all 0.3s ease;
        }
        
        .teacher-tag:hover {
            background: var(--primary);
            color: white;
            transform: translateY(-2px);
        }
        
        .teacher-stats {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }
        
        .stat-item {
            text-align: center;
            flex: 1;
            padding: 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(67, 97, 238, 0.1);
        }
        
        .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: var(--primary);
            font-family: 'Noto Serif SC', serif;
        }
        
        .stat-label {
            font-size: 15px;
            color: var(--text-light);
            margin-top: 5px;
            font-weight: 500;
        }
        
        /* 内容部分 */
        .section {
            background: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: var(--shadow);
            position: relative;
            overflow: hidden;
            animation: fadeIn 1s ease;
        }
        
        .section::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: var(--gradient);
        }
        
        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            color: var(--secondary);
        }
        
        .section-title i {
            margin-right: 15px;
            color: var(--primary);
            font-size: 28px;
            background: rgba(67, 97, 238, 0.1);
            width: 55px;
            height: 55px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .section-title h2 {
            font-family: 'Noto Serif SC', serif;
            font-size: 28px;
            font-weight: 700;
        }
        
        .section-content {
            line-height: 1.8;
            color: var(--text);
            font-size: 17px;
            padding-left: 10px;
        }
        
        .highlight {
            background: linear-gradient(to right, rgba(247, 37, 133, 0.1), transparent);
            padding: 2px 10px;
            border-radius: 4px;
            font-weight: 600;
            color: var(--accent);
        }
        
        /* 教学风格 */
        .teaching-styles {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 25px;
            margin-top: 25px;
        }
        
        .style-card {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px 25px;
            text-align: center;
            transition: all 0.4s ease;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(67, 97, 238, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .style-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: var(--gradient);
        }
        
        .style-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 30px rgba(67, 97, 238, 0.2);
        }
        
        .style-card i {
            font-size: 40px;
            color: var(--primary);
            margin-bottom: 20px;
            background: rgba(67, 97, 238, 0.1);
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            transition: all 0.4s ease;
        }
        
        .style-card:hover i {
            background: var(--gradient);
            color: white;
            transform: scale(1.1);
        }
        
        .style-title {
            font-weight: 700;
            margin-bottom: 15px;
            color: var(--secondary);
            font-family: 'Noto Serif SC', serif;
            font-size: 20px;
            letter-spacing: 0.5px;
        }
        
        .style-card p {
            color: var(--text-light);
            font-size: 15px;
            line-height: 1.6;
        }
        
        /* 评价部分 */
        .reviews-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }
        
        .review-item {
            background: #f8f9ff;
            border-radius: 18px;
            padding: 30px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(67, 97, 238, 0.1);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }
        
        .review-item::after {
            content: """;
            position: absolute;
            top: 20px;
            left: 20px;
            font-size: 80px;
            color: rgba(67, 97, 238, 0.05);
            font-family: Georgia, serif;
            line-height: 1;
        }
        
        .review-item:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 30px rgba(67, 97, 238, 0.15);
        }
        
        .review-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            position: relative;
            z-index: 2;
        }
        
        .review-avatar {
            width: 55px;
            height: 55px;
            border-radius: 50%;
            margin-right: 15px;
            object-fit: cover;
            border: 2px solid white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .review-info {
            flex: 1;
        }
        
        .review-name {
            font-weight: 700;
            color: var(--text);
            font-size: 18px;
        }
        
        .review-date {
            font-size: 14px;
            color: var(--text-light);
        }
        
        .review-stars {
            color: #ffc107;
            margin: 20px 0;
            font-size: 18px;
        }
        
        .review-content {
            font-size: 16px;
            color: var(--text);
            line-height: 1.8;
            position: relative;
            z-index: 2;
            font-style: italic;
            padding: 15px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        
        .screenshot-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 20px;
            margin-top: 35px;
        }
        
        .screenshot {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            aspect-ratio: 3/4;
            background: linear-gradient(135deg, #f0f4ff 0%, #e6ebff 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-light);
            font-size: 15px;
            border: 2px dashed rgba(67, 97, 238, 0.3);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }
        
        .screenshot:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 30px rgba(67, 97, 238, 0.2);
            border: 2px dashed var(--primary);
        }
        
        .screenshot i {
            font-size: 42px;
            color: rgba(67, 97, 238, 0.3);
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .screenshot:hover i {
            color: var(--primary);
            transform: scale(1.1);
        }
        
        .screenshot div {
            text-align: center;
            padding: 20px;
        }
        
        /* 页脚 */
        footer {
            text-align: center;
            padding: 40px 20px 20px;
            color: var(--text-light);
            font-size: 15px;
            margin-top: 20px;
        }
        
        .contact-btn {
            display: inline-block;
            background: var(--gradient);
            color: white;
            padding: 18px 50px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 20px;
            margin: 40px 0;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 15px 30px rgba(67, 97, 238, 0.4);
            position: relative;
            overflow: hidden;
            font-family: 'Noto Serif SC', serif;
            letter-spacing: 1px;
        }
        
        .contact-btn::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: 0.5s;
        }
        
        .contact-btn:hover::before {
            left: 100%;
        }
        
        .contact-btn:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 20px 40px rgba(67, 97, 238, 0.5);
        }
        
        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from { 
                opacity: 0; 
                transform: translateY(30px);
            }
            to { 
                opacity: 1; 
                transform: translateY(0);
            }
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .teacher-header {
                flex-direction: column;
                text-align: center;
                padding: 30px 20px;
            }
            
            .teacher-avatar {
                margin-right: 0;
                margin-bottom: 30px;
            }
            
            .teacher-stats {
                flex-direction: column;
                gap: 15px;
                margin-top: 20px;
            }
            
            .section {
                padding: 30px 25px;
            }
            
            .teaching-styles {
                grid-template-columns: 1fr;
            }
            
            .logo {
                font-size: 36px;
            }
            
            .teacher-name {
                font-size: 30px;
            }
        }
        
        @media (max-width: 480px) {
            .teacher-name {
                font-size: 26px;
            }
            
            .teacher-subject {
                font-size: 20px;
            }
            
            .logo {
                font-size: 32px;
            }
            
            .contact-btn {
                padding: 16px 40px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">菁英<span>教育</span></div>
            <div class="subtitle">EXCELLENCE IN EDUCATION</div>
        </header>
        
        <!-- 教师资料卡片 -->
        <div class="teacher-card">
            <div class="teacher-header">
                <img src="https://images.unsplash.com/photo-1544717305-2782549b5136?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80" alt="王雅文老师" class="teacher-avatar">
                <div class="teacher-info">
                    <h1 class="teacher-name">王雅文 老师</h1>
                    <div class="teacher-subject">高中语文 | 特级教师</div>
                    
                    <div class="teacher-tags">
                        <span class="teacher-tag">文学硕士</span>
                        <span class="teacher-tag">省级名师</span>
                        <span class="teacher-tag">学科带头人</span>
                        <span class="teacher-tag">高考阅卷组</span>
                    </div>
                    
                    <div class="teacher-stats">
                        <div class="stat-item">
                            <div class="stat-value">15年</div>
                            <div class="stat-label">教学经验</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">97%</div>
                            <div class="stat-label">提分率</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">4.99</div>
                            <div class="stat-label">学员评分</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 教师简介 -->
        <div class="section">
            <div class="section-title">
                <i class="fas fa-user-graduate"></i>
                <h2>教师简介</h2>
            </div>
            <div class="section-content">
                <p>王雅文老师拥有北京大学中文系硕士学位，15年一线教学经验，省级特级教师，语文学科带头人。曾担任高考语文阅卷组组长，深谙高考命题规律与评分标准。</p>
                <p>王老师独创<span class="highlight">"三维一体"语文教学法</span>，将文学素养、应试技巧与思维训练完美结合。她善于引导学生感悟文字之美，培养深度阅读能力，同时针对高考考点进行精准突破。</p>
                <p>教学成果：所带班级高考语文平均分132分，培养出5名高考作文满分学生，30余名学生考入北大、清华等顶尖学府中文系。</p>
            </div>
        </div>
        
        <!-- 伴学精力 -->
        <div class="section">
            <div class="section-title">
                <i class="fas fa-calendar-alt"></i>
                <h2>伴学时间</h2>
            </div>
            <div class="section-content">
                <p>王老师每周提供<span class="highlight">精品小班课</span>和<span class="highlight">VIP一对一</span>辅导：</p>
                <ul style="padding-left: 25px; margin: 25px 0;">
                    <li>每周一、三、五 <span class="highlight">19:00-21:00 (现代文阅读精讲)</span></li>
                    <li>每周二、四 <span class="highlight">19:00-21:00 (古文鉴赏与写作)</span></li>
                    <li>周六全天 <span class="highlight">9:00-17:00 (1对1深度辅导)</span></li>
                    <li>每月两次 <span class="highlight">高考作文特训营</span></li>
                </ul>
                <p>为保证教学质量，王老师每学期仅招收<span class="highlight">20名VIP学员</span>，为每位学员建立个性化学习档案，每周提供学习评估报告，48小时内批改作文并反馈。</p>
            </div>
        </div>
        
        <!-- 伴学风格 -->
        <div class="section">
            <div class="section-title">
                <i class="fas fa-chalkboard-teacher"></i>
                <h2>教学风格</h2>
            </div>
            <div class="teaching-styles">
                <div class="style-card">
                    <i class="fas fa-book-open"></i>
                    <div class="style-title">深度阅读</div>
                    <p>引导文本深度解析，培养文学鉴赏能力</p>
                </div>
                <div class="style-card">
                    <i class="fas fa-feather-alt"></i>
                    <div class="style-title">创意写作</div>
                    <p>激发创作灵感，提升写作技巧与文采</p>
                </div>
                <div class="style-card">
                    <i class="fas fa-brain"></i>
                    <div class="style-title">思维训练</div>
                    <p>培养批判性思维与逻辑表达能力</p>
                </div>
                <div class="style-card">
                    <i class="fas fa-heart"></i>
                    <div class="style-title">人文关怀</div>
                    <p>关注学生成长，激发内在学习动力</p>
                </div>
            </div>
        </div>
        
        <!-- 好评反馈 -->
        <div class="section">
            <div class="section-title">
                <i class="fas fa-star"></i>
                <h2>学员评价</h2>
            </div>
            
            <div class="reviews-container">
                <div class="review-item">
                    <div class="review-header">
                        <img src="https://randomuser.me/api/portraits/women/32.jpg" class="review-avatar">
                        <div class="review-info">
                            <div class="review-name">刘同学</div>
                            <div class="review-date">2023-07-18</div>
                        </div>
                    </div>
                    <div class="review-stars">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="review-content">
                        "王老师的课让我重新认识了语文！以前觉得古文枯燥，现在能体会到文字背后的历史和文化。高考语文132分，感谢王老师的悉心指导！"
                    </div>
                </div>
                
                <div class="review-item">
                    <div class="review-header">
                        <img src="https://randomuser.me/api/portraits/men/22.jpg" class="review-avatar">
                        <div class="review-info">
                            <div class="review-name">张同学</div>
                            <div class="review-date">2023-06-30</div>
                        </div>
                    </div>
                    <div class="review-stars">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="review-content">
                        "跟随王老师学习半年，我的作文从42分提升到58分！她教授的写作框架和素材运用方法非常实用，高考作文几乎押中题目，太神奇了！"
                    </div>
                </div>
            </div>
            
            <h3 style="margin: 35px 0 25px; color: var(--secondary); font-family: 'Noto Serif SC', serif; text-align: center; font-size: 24px;">
                <i class="fas fa-camera" style="margin-right: 10px;"></i>学员反馈截图
            </h3>
            <div class="screenshot-grid">
                <div class="screenshot">
                    <div>
                        <i class="fas fa-image"></i>
                        <div>反馈截图1</div>
                    </div>
                </div>
                <div class="screenshot">
                    <div>
                        <i class="fas fa-image"></i>
                        <div>反馈截图2</div>
                    </div>
                </div>
                <div class="screenshot">
                    <div>
                        <i class="fas fa-image"></i>
                        <div>反馈截图3</div>
                    </div>
                </div>
                <div class="screenshot">
                    <div>
                        <i class="fas fa-image"></i>
                        <div>反馈截图4</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin: 30px 0 50px;">
            <a href="#" class="contact-btn">预约王老师课程</a>
        </div>
        
        <footer>
            <p>© 2023 菁英教育 版权所有 | 以文载道 · 以教育人</p>
            <p>咨询电话: 400-567-8910 | 邮箱: <EMAIL></p>
        </footer>
    </div>
    
    <script>
        // 添加滚动动画效果
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.section');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.animation = 'fadeIn 1s ease forwards';
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.1 });
            
            sections.forEach(section => {
                observer.observe(section);
            });
            
            // 添加按钮波纹效果
            const contactBtn = document.querySelector('.contact-btn');
            contactBtn.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 创建波纹效果
                const ripple = document.createElement('span');
                ripple.classList.add('ripple');
                this.appendChild(ripple);
                
                // 获取点击位置
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size/2;
                const y = e.clientY - rect.top - size/2;
                
                // 设置波纹位置和大小
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                
                // 移除波纹元素
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    </script>
</body>
</html>